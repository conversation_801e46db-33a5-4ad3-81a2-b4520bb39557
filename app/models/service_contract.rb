# ABOUTME: ServiceContract is a parent model for Works to handle multi-day, non-consecutive work scheduling
# ABOUTME: Every Work must belong to a ServiceContract, providing aggregate time tracking and status management
class ServiceContract < ApplicationRecord
  acts_as_tenant(:company)
  
  belongs_to :company
  belongs_to :client, optional: true
  has_many :works, dependent: :destroy
  has_many :work_assignments, through: :works
  has_many :work_sessions, through: :works
  
  validates :title, presence: true
  validates :company, presence: true
  validates :status, presence: true
  
  enum status: {  
    scheduled: "scheduled", 
    in_progress: "in_progress", 
    completed: "completed", 
    cancelled: "cancelled",
    unprocessed: "unprocessed"
  }
  
  # Aggregate time calculation across all works - returns minutes for frontend compatibility
  def total_time_spent
    total_seconds = work_sessions.sum { |ws| ws.duration || 0 }
    (total_seconds.to_f / 60).round
  end
  
  # Time spent by a specific user - returns minutes for frontend compatibility
  def total_time_spent_by_user(user)
    total_seconds = work_sessions.where(user: user).sum { |ws| ws.duration || 0 }
    (total_seconds.to_f / 60).round
  end

  def total_work_sessions_time
    work_sessions.sum(:duration) || 0
  end
  
  # Dynamic status based on works statuses
  def calculated_status
    work_statuses = works.pluck(:status)
    return 'completed' if work_statuses.all? { |s| s == 'completed' }
    return 'in_progress' if work_statuses.any? { |s| s == 'in_progress' }
    'scheduled'
  end
  
  # Generate generic title format: year-month-company_id-id
  def self.generate_generic_title(company_id, id = nil)
    today = Date.current
    year_month = today.strftime("%Y-%m")
    contract_id = id || "#{Time.current.to_i}"
    "#{year_month}-#{company_id}-#{contract_id}"
  end
end