import { createRouter, createWebHistory } from 'vue-router'
import store from '../store'

// Import layouts
import DefaultLayout from '../layouts/DefaultLayout.vue'
import AuthLayout from '../layouts/AuthLayout.vue'
import PublicLayout from '../layouts/PublicLayout.vue'

// Define routes with lazy loading
const routes = [
  // Root redirect to default locale
  {
    path: '/',
    redirect: '/cs'
  },
  
  // Auth routes with locale support
  {
    path: '/:locale(cs|sk|en)/users/sign_in',
    component: AuthLayout,
    children: [
      {
        path: '',
        name: 'login',
        component: () => import('../views/auth/LoginView.vue')
      }
    ]
  },
  {
    path: '/:locale(cs|sk|en)/users/sign_up',
    component: AuthLayout,
    children: [
      {
        path: '',
        name: 'register',
        component: () => import('../views/auth/RegisterView.vue')
      }
    ]
  },
  {
    path: '/:locale(cs|sk|en)/forgot-password',
    component: AuthLayout,
    children: [
      {
        path: '',
        name: 'forgotPassword',
        component: () => import('../views/auth/ForgotPasswordView.vue')
      }
    ]
  },
  {
    path: '/:locale(cs|sk|en)/reset-password',
    component: AuthLayout,
    children: [
      {
        path: '',
        name: 'passwordReset',
        component: () => import('../views/auth/PasswordResetView.vue')
      }
    ]
  },
  {
    path: '/:locale(cs|sk|en)/users/confirmation/new',
    component: AuthLayout,
    children: [
      {
        path: '',
        name: 'resendConfirmation',
        component: () => import('../views/auth/ResendConfirmationView.vue')
      }
    ]
  },
  {
    path: '/:locale(cs|sk|en)/confirm-email',
    component: AuthLayout,
    children: [
      {
        path: '',
        name: 'emailConfirmation',
        component: () => import('../views/auth/EmailConfirmationView.vue')
      }
    ]
  },
  // JWT-based invitation routes
  {
    path: '/:locale(cs|sk|en)/auth/accept-invitation',
    component: AuthLayout,
    children: [
      {
        path: '',
        name: 'acceptInvitation',
        component: () => import('../views/auth/AcceptInvitationView.vue')
      }
    ]
  },
  {
    path: '/:locale(cs|sk|en)/auth/accept-company-invitation',
    component: AuthLayout,
    children: [
      {
        path: '',
        name: 'acceptCompanyInvitation',
        component: () => import('../views/auth/AcceptCompanyInvitationView.vue')
      }
    ]
  },
  
  // Public routes with locale support
  {
    path: '/:locale(cs|sk|en)/public',
    component: PublicLayout,
    children: [
      {
        path: 'bookings/:token',
        name: 'publicBooking',
        component: () => import('../views/public/PublicBookingView.vue')
      },
      {
        path: 'meetings/:token',
        name: 'publicMeeting',
        component: () => import('../views/public/PublicMeetingView.vue')
      }
    ]
  },
  
  // Protected routes with locale support
  {
    path: '/:locale(cs|sk|en)',
    component: DefaultLayout,
    meta: { requiresAuth: true },
    children: [
      // Root redirect within locale
      {
        path: '',
        redirect: to => `/${to.params.locale}/dashboard`
      },
      
      // Core routes
      {
        path: 'dashboard',
        name: 'dashboard',
        component: () => import('../views/DashboardView.vue')
      },
      {
        path: 'mainbox',
        name: 'mainbox',
        component: () => import('../views/MainboxView.vue')
      },
      
      // Bookings
      {
        path: 'bookings',
        name: 'bookings',
        component: () => import('../views/bookings/BookingsIndexView.vue')
      },
      {
        path: 'bookings/:id',
        name: 'bookingShow',
        component: () => import('../views/bookings/BookingShowView.vue')
      },
      {
        path: 'booking_links',
        name: 'bookingLinks',
        component: () => import('../views/bookings/BookingLinksView.vue')
      },
      {
        path: 'booking_links/:id',
        name: 'bookingLinkShow',
        component: () => import('../views/bookings/BookingLinkShowView.vue')
      },
      
      // Meetings
      {
        path: 'meetings',
        name: 'meetings',
        component: () => import('../views/meetings/MeetingsIndexView.vue')
      },
      {
        path: 'meetings/:id',
        name: 'meetingShow',
        component: () => import('../views/meetings/MeetingShowView.vue')
      },
      
      // Daily Logs
      {
        path: 'daily_logs',
        name: 'dailyLogs',
        component: () => import('../views/dailylogs/DailyLogsIndexView.vue')
      },
      {
        path: 'daily_logs/report',
        name: 'dailyLogsReport',
        component: () => import('../views/dailylogs/DailyLogsReportView.vue')
      },
      
      // Reports
      {
        path: 'reports/owner-monthly/:contractId?',
        name: 'ownerMonthlyReports',
        component: () => import('../views/reports/OwnerMonthlyReportsView.vue'),
        meta: { requiresAuth: true, requiresManager: true }
      },
      {
        path: 'reports/owner-work-summary',
        name: 'ownerWorkSummary',
        component: () => import('../views/reports/OwnerWorkSummaryView.vue'),
        meta: { requiresAuth: true, requiresManager: true }
      },
      {
        path: 'reports/service-contract-summary',
        name: 'serviceContractSummary',
        component: () => import('../components/reports/ServiceContractSummary.vue'),
        meta: { requiresAuth: true, requiresManager: true }
      },
      {
        path: 'reports/works-summary',
        name: 'worksSummary',
        component: () => import('../views/reports/WorksSummaryView.vue'),
        meta: { requiresAuth: true, requiresManager: true }
      },
      
      // Works
      {
        path: 'works',
        name: 'works',
        component: () => import('../views/works/WorksIndexView.vue')
      },
      {
        path: 'works/:id',
        name: 'workShow',
        component: () => import('../views/works/WorkShowView.vue')
      },
      
      // Events
      {
        path: 'events',
        name: 'events',
        component: () => import('../views/events/NewEventsIndexView.vue')
      },
      {
        path: 'events/weekly',
        name: 'eventsWeekly',
        component: () => import('../views/events/WeeklyEventsView.vue')
      },
      
      // Contracts
      {
        path: 'contracts',
        name: 'contracts',
        component: () => import('../views/contracts/ContractsIndexView.vue')
      },
      {
        path: 'contracts/:id',
        name: 'contractShow',
        component: () => import('../views/contracts/ContractShowView.vue')
      },
      
      // Companies
      {
        path: 'companies',
        name: 'companies',
        component: () => import('../views/companies/CompaniesIndexView.vue')
      },
      {
        path: 'companies/:id/edit',
        name: 'companyEdit',
        component: () => import('../views/companies/CompanyEditView.vue')
      },
      {
        path: 'company_connections',
        name: 'companyConnections',
        component: () => import('../views/companies/CompanyConnectionsView.vue')
      },
      {
        path: 'company_settings/edit',
        name: 'companySettings',
        component: () => import('../views/companies/CompanySettingsView.vue')
      },
      
      // Reports
      {
        path: 'reports/activities',
        name: 'activitiesReport',
        component: () => import('../views/reports/ActivitiesReportView.vue')
      },
      
      // User
      {
        path: 'user_profiles/:id/edit',
        name: 'userProfileEdit',
        component: () => import('../views/user/UserProfileEditView.vue')
      },
      {
        path: 'user_settings/edit',
        name: 'userSettings',
        component: () => import('../views/user/UserSettingsView.vue')
      },
      
      // Holidays
      {
        path: 'holidays',
        name: 'holidays',
        component: () => import('../views/HolidaysView.vue')
      },
      
      // Assignments
      {
        path: 'assignments',
        name: 'assignments',
        component: () => import('../views/AssignmentsView.vue')
      }
    ]
  },
  
  // Legacy auth routes redirect to locale-specific routes
  {
    path: '/login',
    redirect: '/cs/users/sign_in'
  },
  {
    path: '/users/sign_in',
    redirect: '/cs/users/sign_in'
  },
  {
    path: '/users/sign_up',
    redirect: '/cs/users/sign_up'
  },
  {
    path: '/users/password/new',
    redirect: '/cs/forgot-password'
  },
  {
    path: '/users/password/edit',
    redirect: to => {
      // Preserve the reset_password_token query parameter
      const token = to.query.reset_password_token;
      return token ? `/cs/reset-password?token=${token}` : '/cs/reset-password';
    }
  },
  

  
  // 404 handler - must be last
  {
    path: '/:pathMatch(.*)*',
    name: 'notFound',
    component: () => import('../views/NotFoundView.vue')
  }
]

// Create router instance
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// Navigation guards
router.beforeEach(async (to, from, next) => {
  // Extract locale from path
  const locale = to.params.locale || 'cs'
  
  // Update i18n locale if available
  if (typeof window !== 'undefined' && window.$i18n) {
    window.$i18n.locale = locale
  }
  
  // Check if route requires authentication
  if (to.matched.some(record => record.meta.requiresAuth)) {
    // Check if user is authenticated
    const isAuthenticated = store.getters['userStore/isAuthenticated']
    
    if (!isAuthenticated) {
      // Try to restore session from API using AuthService
      try {
        console.log('[DEBUG] Router: Attempting to restore authentication')
        const { default: AuthService } = await import('../services/authService')
        const restored = await AuthService.ensureAuthenticated()
        
        if (restored) {
          console.log('[DEBUG] Router: Authentication restored successfully')
          next()
        } else {
          console.log('[DEBUG] Router: Authentication restoration failed, redirecting to login')
          // Redirect to locale-specific login page
          next({ 
            path: `/${locale}/users/sign_in`, 
            query: { redirect: to.fullPath } 
          })
        }
      } catch (error) {
        console.error('[DEBUG] Router: Auth check failed:', error)
        // Redirect to locale-specific login page
        next({ 
          path: `/${locale}/users/sign_in`, 
          query: { redirect: to.fullPath } 
        })
      }
    } else {

      next()
    }
  } else {
    next()
  }
})

// Global error handler
router.onError((error) => {
  console.error('Router error:', error)
  // You can add more sophisticated error handling here
  // For example, redirect to error page or show notification
})

export default router