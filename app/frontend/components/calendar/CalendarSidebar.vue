<!-- ABOUTME: Right sidebar for calendar showing unscheduled works/bookings or detailed item view -->
<!-- ABOUTME: Includes drag-and-drop functionality and detailed views for calendar items -->

<template>
  <div class="calendar-sidebar">
    <!-- Navigation Section -->
    <div class="sidebar-navigation">
      
      <div class="secondary-actions">
        <button @click="createNewWork" class="btn btn-small btn-outline">
          <LandPlot :size="14" />
          {{ $t('works.new', 'Nová práce') }}
        </button>
        <button @click="createNewEvent" class="btn btn-small btn-outline">
          <Plus :size="12" />
          {{ $t('events.new', 'Událost') }}
        </button>
        <LocalizedLink :to="'/holidays'" class="btn btn-small btn-outline" :use-anchor="true">
          {{ $t('holidays', 'Svátky') }}
        </LocalizedLink>
      </div>
    </div>

    <!-- Organization Section -->
    <div class="organization-section">
      <!-- Show organization list or detail view -->
      <div v-if="sidebarView === 'unscheduled'" class="organization-list">
        <div class="section-header" @click="toggleOrganization">
          <h4 class="section-title">
            <LandPlot :size="16" />
            {{ $t('works.unscheduled', 'Práce bez termínu') }}
            <span class="count">({{ unscheduledWorks.length }})</span>
          </h4>
          <button class="toggle-btn">
            <ChevronDown :size="16" :class="{ 'rotated': !organizationExpanded }" />
          </button>
        </div>
        
        <div v-if="organizationExpanded" class="organization-content">
          <div v-if="unscheduledWorks.length === 0" class="empty-state">
            {{ $t('works.no_unscheduled', 'Žádné neplánované práce') }}
          </div>
          
          <draggable
            v-else
            v-model="unscheduledWorksLocal"
            :group="{ name: 'calendar-items', pull: 'clone', put: false }"
            item-key="id"
            class="unscheduled-list"
            :sort="false"
            @start="onDragStart"
            @end="onDragEnd"
          >
            <template #item="{ element }">
              <UnscheduledWorkCard 
                :work="{ ...element, itemType: 'work', uniqueId: `unscheduled-work-${element.id}` }"
                @click="$emit('work-clicked', element)"
              />
            </template>
          </draggable>
        </div>
      </div>
      
      <!-- Detail View within organization section -->
      <div v-else-if="sidebarView === 'detail'" class="organization-detail">
        <div class="detail-header">
          <button @click="closeSidebar" class="close-button">
            <X :size="16" />
          </button>
          <span class="detail-title">{{ getDetailTitle() }}</span>
        </div>

        <div class="detail-content">
          <!-- Work Detail -->
          <WorkShow 
            v-if="selectedItemType === 'work'" 
            :work="selectedItem"
            @updated="$emit('item-updated')"
          />
          
          <!-- Event Detail -->
          <div v-else-if="selectedItemType === 'event'" class="event-detail-view">
            <EventCard 
              :event="selectedItem"
              @click="() => {}"
              @event-updated="$emit('item-updated')"
              @delete-requested="handleDeleteEvent"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex';
import { Plus, LandPlot, X, ChevronDown } from 'lucide-vue-next';
import draggable from 'vuedraggable';
import UnscheduledWorkCard from './UnscheduledWorkCard.vue';
import EventCard from './EventCard.vue';
import WorkShow from '../works/WorkShow.vue';
import LocalizedLink from '../LocalizedLink.vue';

export default {
  name: 'CalendarSidebar',
  components: {
    Plus,
    LandPlot,
    X,
    ChevronDown,
    draggable,
    UnscheduledWorkCard,
    EventCard,
    WorkShow,
    LocalizedLink
  },
  data() {
    return {
      unscheduledWorksLocal: [],
      organizationExpanded: true
    };
  },
  computed: {
    ...mapState('calendarStore', ['sidebarView', 'selectedItem', 'selectedItemType']),
    ...mapGetters('calendarStore', ['unscheduledWorks'])
  },
  watch: {
    unscheduledWorks: {
      handler(newWorks) {
        this.unscheduledWorksLocal = [...newWorks];
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    ...mapActions('calendarStore', ['closeSidebar', 'deleteEvent', 'rejectEvent']),
    
    toggleOrganization() {
      this.organizationExpanded = !this.organizationExpanded;
    },
    
    createNewWork() {
      // Emit event to parent to handle work creation
      this.$emit('create-work');
    },
    
    createNewEvent() {
      // Emit event to parent to handle event creation
      this.$emit('create-event');
    },
    
    getDetailTitle() {
      if (this.selectedItemType === 'work') {
        return this.selectedItem?.title || this.selectedItem?.name || this.$t('works.detail', 'Detail práce');
      } else if (this.selectedItemType === 'event') {
        return this.selectedItem?.title || this.$t('events.detail', 'Detail události');
      }
      return this.$t('details', 'Detaily');
    },
    
    async handleDeleteEvent(eventId) {
      try {
        await this.deleteEvent(eventId);
        this.$emit('item-updated');
        this.closeSidebar();
      } catch (error) {
        // flash message is handled in the store action
      }
    },

    formatCreatedDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleDateString(this.$i18n.locale, { 
        month: 'short', 
        day: 'numeric' 
      });
    },
    
    formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleDateString(this.$i18n.locale, { 
        weekday: 'short',
        month: 'short', 
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    },
    
    onDragStart(event) {
      console.log('📋 Drag started from sidebar:', event.item);
    },
    
    onDragEnd(event) {
      console.log('📋 Drag ended from sidebar. To:', event.to?.className, 'From:', event.from?.className);
      
      // If dropped on calendar (target has drop-zone class), the item should be scheduled
      if (event.to && event.to.classList.contains('drop-zone')) {
        console.log('📅 Item was dropped on calendar - should be scheduled');
        // The calendar will handle the scheduling, refresh after a short delay
        setTimeout(() => {
          this.unscheduledWorksLocal = [...this.unscheduledWorks];
        }, 500);
      } else {
        // Reset local array to prevent stale state
        this.unscheduledWorksLocal = [...this.unscheduledWorks];
      }
    }
  }
};
</script>

<style scoped>
.calendar-sidebar {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: white;
}

/* Navigation Section */
.sidebar-navigation {
  background: white;
  padding: 1rem;
  flex-shrink: 0;
  border-bottom: 1px solid #e5e7eb;
}

.secondary-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.secondary-actions .btn {
  gap: 0.375rem;
}

/* Organization Section - Unified styling */
.organization-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: white;
}

.organization-list,
.organization-detail {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: white;
}

/* Unified section header - larger clickable area */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  padding: 1rem;
  margin: 0;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  flex-shrink: 0;
}

.toggle-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.375rem;
  display: flex;
  align-items: center;
  color: #6b7280;
  min-width: 2rem;
  min-height: 2rem;
}

.rotated {
  transform: rotate(-90deg);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #374151;
  margin: 0;
  font-size: 0.875rem;
  font-weight: 600;
}

.count {
  color: #6b7280;
  font-weight: normal;
}

/* Unified content areas */
.organization-content,
.detail-content {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  background: white;
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

.organization-content::-webkit-scrollbar,
.detail-content::-webkit-scrollbar {
  width: 6px;
}

.organization-content::-webkit-scrollbar-track,
.detail-content::-webkit-scrollbar-track {
  background: transparent;
}

.organization-content::-webkit-scrollbar-thumb,
.detail-content::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: 3px;
}

.organization-content:hover::-webkit-scrollbar-thumb,
.detail-content:hover::-webkit-scrollbar-thumb {
  background-color: #cbd5e0;
}

.organization-content::-webkit-scrollbar-thumb:hover,
.detail-content::-webkit-scrollbar-thumb:hover {
  background-color: #a0aec0;
}

.empty-state {
  text-align: center;
  color: #6b7280;
  padding: 2rem 1rem;
  font-style: italic;
}

.unscheduled-list {
  min-height: 50px;
}

.booking-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.booking-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.booking-title {
  color: #1f2937;
  margin-bottom: 4px;
}

.booking-date {
  color: #6b7280;
  margin-bottom: 4px;
}

.booking-duration {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #6b7280;
}

/* Detail header - unified with section header */
.detail-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
  background: white;
  flex-shrink: 0;
}

.detail-title {
  color: #374151;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 0.875rem;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.375rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  flex-shrink: 0;
  min-width: 2rem;
  min-height: 2rem;
}

.meeting-detail h4 {
  margin: 0 0 1rem 0;
  color: #1f2937;
}

.meeting-detail p {
  margin-bottom: 1rem;
  color: #6b7280;
}

.meeting-detail div {
  margin-bottom: 0.5rem;
}

</style>